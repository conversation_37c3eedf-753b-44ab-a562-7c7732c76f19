// Polyfill for useSyncExternalStore
import { useSyncExternalStore } from 'use-sync-external-store/shim';

// Ensure React has useSyncExternalStore available
declare global {
  interface Window {
    React: any;
  }
}

if (typeof window !== 'undefined') {
  // Import React to ensure it's available
  import('react').then((React) => {
    if (!React.useSyncExternalStore) {
      (React as any).useSyncExternalStore = useSyncExternalStore;
    }
    
    // Also make it available globally
    if (!window.React) {
      window.React = React;
    } else if (!window.React.useSyncExternalStore) {
      window.React.useSyncExternalStore = useSyncExternalStore;
    }
  });
}
