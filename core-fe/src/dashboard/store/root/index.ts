import axios, { AxiosInstance } from "axios";
import { create } from "zustand";

import { CommonApi } from "@/dashboard/api/common.api";
import { ShopInfoDto } from "../../models/common/shop-data.model";

// Store state interface
interface RootState {
  isLoading: boolean;
  shopInfo: ShopInfoDto | null;
  error: boolean;
  authAxios: AxiosInstance;
}

// Store actions interface
interface RootActions {
  setLoading: (loading: boolean) => void;
  setShopInfo: (shopInfo: ShopInfoDto | null) => void;
  setError: (error: boolean) => void;
  initializeRoot: () => Promise<void>;
}

// Combined store interface
type RootStore = RootState & RootActions;

// Initial state
const initialState: Omit<RootState, "authAxios"> = {
  isLoading: true,
  shopInfo: null,
  error: false
};

// Create the Zustand store
export const rootStore = create<RootStore>()((set, get) => ({
  // State
  ...initialState,
  authAxios: axios.create({
    baseURL: import.meta.env.VITE_BACKEND_END_POINT,
    timeout: 30 * 3600
  }),

  // Actions
  setLoading: (loading: boolean) => set({ isLoading: loading }),
  setShopInfo: (shopInfo: ShopInfoDto | null) => set({ shopInfo }),
  setError: (error: boolean) => set({ error }),

  addInterceptorAxios: (jwt: string) => {
    const instance = get().authAxios;
    instance.interceptors.request.use((config: any) => {
      // add token to request headers
      config.headers.Authorization = `Bearer ${jwt}`;
      return config;
    });

    instance.interceptors.response.use(
      (response: any) => {
        return response;
      },
      (error: any) => {
        return Promise.reject(error);
      }
    );
    set({ authAxios: instance });
  },

  initializeRoot: async () => {
    const { setLoading, setError, setShopInfo } = get();

    try {
      setLoading(true);
      setError(false);

      const urlParams = new URLSearchParams(window.location.search);
      const instance = urlParams.get("instance");

      if (!instance) {
        setError(true);
        return;
      }

      const payload = instance.split(".")[1];
      const decoded = JSON.parse(atob(payload));

      if (!decoded?.instanceId) {
        setError(true);
        return;
      }

      // Fetch shop info
      const shopData = await CommonApi.GetShop(decoded.instanceId);

      if (shopData.result) {
        setShopInfo(shopData.result);
      } else {
        setError(true);
      }
    } catch (error) {
      console.error("Failed to initialize root:", error);
      setError(true);
    } finally {
      setLoading(false);
    }
  }
}));
