import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "../lib/api-handler";
import { passParams } from "../lib/pass-params";
import { ShopInfoDto } from "../models/common/shop-data.model";

export class CommonApi {
  static async GetShop(instanceId: string) {
    const response = await ApiHandler<ShopInfoDto>({
      method: "GET",
      url: passParams("/common/shop-info", { instanceId })
    });
    return response;
  }
}
