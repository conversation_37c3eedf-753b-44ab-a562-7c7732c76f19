import React, { useEffect, type FC } from "react";

import "@wix/design-system/styles.global.css";
import "../../polyfills";
import Home from "../components/home";
import { rootStore } from "../store/root";
import "../styles/global.css";

const Index: FC = () => {
  const { initializeRoot } = rootStore();

  useEffect(() => {
    initializeRoot();
  }, []);

  return <Home />;
};

export default Index;
