# core-fe

A modern dashboard extension for Wix, built with React, <PERSON>ustand, and the Wix Design System. This project provides a foundation for building management capabilities and custom dashboard pages within the Wix ecosystem.

## Project Purpose

This app is designed to extend the Wix dashboard with custom pages, leveraging Wix APIs and the Wix Design System for a seamless, native experience. It demonstrates best practices for state management, API integration, and UI composition in a Wix app context.

## Features

- **Custom Dashboard Page**: A starter dashboard page with Wix Design System components.
- **State Management**: Global state handled by <PERSON>ustand, including loading, error, and shop info.
- **API Integration**: Fetches shop information from a backend using Axios.
- **Wix Design System**: Uses Wix's official design system for consistent UI/UX.
- **Polyfills**: Ensures compatibility with React features across environments.

## Tech Stack

- **React 18**
- **Zustand** for state management
- **Axios** for HTTP requests
- **Wix Design System** for UI components
- **TypeScript**
- **Vite** for build tooling

## Directory Structure

```
src/
  dashboard/
    api/         # API handlers and integrations
    components/  # React components (e.g., Home)
    pages/       # Dashboard page entry and config
    store/       # Zustand state management
    styles/      # Global and component styles
    ...
  polyfills.ts   # Polyfills for React features
```

## Setup 🔧

Install dependencies:

```bash
yarn
```

Start the development server:

```bash
yarn dev
```

## Usage

- The main dashboard page is defined in `src/dashboard/pages/page.tsx` and uses the `Home` component.
- State is managed via `src/dashboard/store/root/index.ts` using Zustand, with API calls to fetch shop info.
- The UI is built with Wix Design System components for a native look and feel.

## Development Notes

- **Polyfills**: `src/polyfills.ts` ensures compatibility for `useSyncExternalStore` in React.
- **Environment Variables**: Backend endpoint is configured via `VITE_BACKEND_END_POINT`.
- **API Example**: See `src/dashboard/api/common.api.ts` for how to fetch shop info.

## References

- [Wix CLI for Apps documentation](https://dev.wix.com/docs/build-apps/developer-tools/cli/get-started/about-the-wix-cli-for-apps)
- [Wix Design System](https://www.wix.com/design-system)
- [Dashboard Extensions Guide](https://dev.wix.com/docs/build-apps/develop-your-app/frameworks/wix-cli/supported-extensions/dashboard-extensions/dashboard-pages/add-dashboard-page-extensions-with-the-cli#add-dashboard-page-extensions-with-the-cli)

---

Feel free to extend this project with more dashboard pages, API integrations, and custom features as needed!
